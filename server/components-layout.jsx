/**
 * Components showcase layout using JSX
 */

export const ComponentsLayout = () => (
  <div class="container">
    <h1>🎨 FeexVeb JSX Components Showcase</h1>
    <p>Explore advanced components built with restored JSX syntax, demonstrating FeexVeb's capabilities with server-side rendering and HTMX integration.</p>

    <div class="components-grid">
      {/* Todo List Components */}
      <section class="component-section">
        <h2 class="section-title">📝 Todo Management</h2>
        <p class="section-description">
          Interactive todo lists showcasing state management, dynamic lists, and HTMX server integration.
        </p>

        <div class="component-demo">
          <h3>Client-Side Todo List</h3>
          <fx-todo-list></fx-todo-list>
        </div>

        <div class="component-demo">
          <h3>Server-Integrated Todo (HTMX)</h3>
          <fx-todo-htmx></fx-todo-htmx>
        </div>
      </section>

      {/* Weather Components */}
      <section class="component-section">
        <h2 class="section-title">🌤️ Weather Widgets</h2>
        <p class="section-description">
          Weather components demonstrating external API integration, loading states, and error handling.
        </p>

        <div class="component-demo">
          <h3>Single Weather Widget</h3>
          <fx-weather-widget city="New York"></fx-weather-widget>
        </div>

        <div class="component-demo">
          <h3>Weather Dashboard</h3>
          <fx-weather-dashboard></fx-weather-dashboard>
        </div>
      </section>

      {/* Form Components */}
      <section class="component-section">
        <h2 class="section-title">📋 Form Validation</h2>
        <p class="section-description">
          Advanced form handling with real-time validation, error states, and user feedback.
        </p>

        <div class="component-demo">
          <fx-contact-form></fx-contact-form>
        </div>
      </section>

      {/* Data Table */}
      <section class="component-section">
        <h2 class="section-title">📊 Data Management</h2>
        <p class="section-description">
          Feature-rich data table with sorting, filtering, pagination, and bulk operations.
        </p>

        <div class="component-demo">
          <fx-data-table></fx-data-table>
        </div>
      </section>

      {/* Chat Components */}
      <section class="component-section">
        <h2 class="section-title">💬 Real-time Chat</h2>
        <p class="section-description">
          Chat components with simulated real-time updates and HTMX-powered messaging.
        </p>

        <div class="component-demo">
          <h3>Interactive Chat Widget</h3>
          <fx-chat-widget username="Demo User"></fx-chat-widget>
        </div>

        <div class="component-demo">
          <h3>HTMX Chat Room</h3>
          <fx-chat-room room-id="showcase"></fx-chat-room>
        </div>
      </section>
    </div>

    {/* JSX Benefits Section */}
    <section class="benefits-section">
      <h2 class="section-title">✨ JSX Syntax Benefits</h2>
      <div class="benefits-grid">
        <div class="benefit-card">
          <h3>🎯 Natural Syntax</h3>
          <p>Write components using familiar HTML-like JSX syntax instead of verbose createElement calls.</p>
          <div class="code-comparison">
            <div class="code-block">
              <h4>JSX (FeexVeb)</h4>
              <pre><code>{`<div class="card">
  <h3>{title}</h3>
  <p>{content}</p>
</div>`}</code></pre>
            </div>
            <div class="code-block">
              <h4>createElement</h4>
              <pre><code>{`createElement('div', {class: 'card'},
  createElement('h3', null, title),
  createElement('p', null, content)
)`}</code></pre>
            </div>
          </div>
        </div>

        <div class="benefit-card">
          <h3>🚀 Server-Side Rendering</h3>
          <p>Components render on the server for faster initial page loads and better SEO.</p>
          <ul>
            <li>Pre-rendered HTML content</li>
            <li>Improved performance</li>
            <li>SEO-friendly markup</li>
            <li>Progressive enhancement</li>
          </ul>
        </div>

        <div class="benefit-card">
          <h3>🔄 HTMX Integration</h3>
          <p>Seamless integration with HTMX for reactive server-side interactions.</p>
          <ul>
            <li>Server-driven updates</li>
            <li>Minimal JavaScript</li>
            <li>Real-time data sync</li>
            <li>Progressive enhancement</li>
          </ul>
        </div>

        <div class="benefit-card">
          <h3>🎨 Component Modularity</h3>
          <p>Build reusable, encapsulated components with clean separation of concerns.</p>
          <ul>
            <li>Reactive state management</li>
            <li>Computed properties</li>
            <li>Event handling</li>
            <li>Lifecycle management</li>
          </ul>
        </div>
      </div>
    </section>

    {/* Technical Details */}
    <section class="technical-section">
      <h2 class="section-title">⚙️ Technical Implementation</h2>
      <div class="tech-details">
        <div class="tech-item">
          <h3>JSX Transpilation</h3>
          <p>Uses Preact's JSX runtime for efficient virtual DOM creation and server-side rendering.</p>
        </div>

        <div class="tech-item">
          <h3>State Management</h3>
          <p>Powered by Maverick.js Signals for reactive state updates and computed properties.</p>
        </div>

        <div class="tech-item">
          <h3>Web Components</h3>
          <p>Built on standard Web Components API for maximum compatibility and encapsulation.</p>
        </div>

        <div class="tech-item">
          <h3>HTMX Integration</h3>
          <p>Seamless server-side interactions with automatic HTMX processing and fragment updates.</p>
        </div>
      </div>
    </section>

    {/* Navigation */}
    <div class="navigation">
      <a href="/" class="nav-link">← Back to Counter Examples</a>
      <a href="/api-docs" class="nav-link">API Documentation →</a>
    </div>
  </div>
);
